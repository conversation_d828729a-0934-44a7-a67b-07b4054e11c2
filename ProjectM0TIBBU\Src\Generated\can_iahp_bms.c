/*
 * CAN_IAHP_BMS - CAN Database Source File
 * Generated from CAN_IAHP_BMS.dbc using cantools
 */

#include "can_iahp_bms.h"
#include <string.h>

/* Helper macros for bit manipulation */
#define SET_SIGNAL(data, value, start_bit, length) \
    do { \
        uint64_t temp_val = (uint64_t)(value); \
        for (int i = 0; i < (length); i++) { \
            if (temp_val & (1ULL << i)) { \
                (data)[((start_bit) + i) / 8] |= (1U << (((start_bit) + i) % 8)); \
            } else { \
                (data)[((start_bit) + i) / 8] &= ~(1U << (((start_bit) + i) % 8)); \
            } \
        } \
    } while(0)

/* Message timing configuration based on GenMsgDelayTime */
can_iahp_bms_msg_timing_t can_iahp_bms_msg_timings[46] = {
    { 0, 50, 0, 0, 1 },  // BMS_PackInfo1: DelayTime=0ms (物理量分组), CycleTime=50ms
    { 0, 50, 0, 0, 1 },  // BMS_PackInfo2: DelayTime=0ms (物理量分组), CycleTime=50ms
    { 0, 50, 0, 0, 1 },  // BMS_PackInfo3: DelayTime=0ms (物理量分组), CycleTime=50ms
    { 0, 50, 0, 0, 1 },  // BMS_PackInfo4: DelayTime=0ms (物理量分组), CycleTime=50ms
    { 0, 50, 0, 0, 1 },  // BMS_PackInfo5: DelayTime=0ms (物理量分组), CycleTime=50ms
    { 0, 50, 0, 0, 1 },  // BMS_PackInfo6: DelayTime=0ms (物理量分组), CycleTime=50ms
    { 0, 50, 0, 0, 1 },  // BMS_PackInfo7: DelayTime=0ms (物理量分组), CycleTime=50ms
    { 0, 50, 0, 0, 1 },  // BMS_PackInfo8: DelayTime=0ms (物理量分组), CycleTime=50ms
    { 1, 50, 0, 0, 1 },  // BMS_Status1: DelayTime=1ms (物理量分组), CycleTime=50ms
    { 1, 50, 0, 0, 1 },  // BMS_Status2: DelayTime=1ms (物理量分组), CycleTime=50ms
    { 1, 50, 0, 0, 1 },  // BMS_Status3: DelayTime=1ms (物理量分组), CycleTime=50ms
    { 1, 50, 0, 0, 1 },  // BMS_Status4: DelayTime=1ms (物理量分组), CycleTime=50ms
    { 1, 50, 0, 0, 1 },  // BMS_Status5: DelayTime=1ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp01_04: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp05_08: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp09_12: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp13_16: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp17_20: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp21_24: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp25_28: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp29_32: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp33_36: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp37_40: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp41_44: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 3, 50, 0, 0, 1 },  // BMS_Temp45_48: DelayTime=3ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell01_04: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell05_08: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell09_12: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell13_16: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell17_20: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell21_24: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell25_28: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell29_32: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell33_36: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell37_40: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell41_44: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell45_48: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell49_52: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell53_56: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell57_60: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell61_64: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell65_68: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 2, 50, 0, 0, 1 },  // BMS_Cell69_72: DelayTime=2ms (物理量分组), CycleTime=50ms
    { 4, 50, 0, 0, 1 },  // BMS_OtherVolt1: DelayTime=4ms (物理量分组), CycleTime=50ms
    { 4, 50, 0, 0, 1 },  // BMS_OtherVolt2: DelayTime=4ms (物理量分组), CycleTime=50ms
    { 5, 50, 0, 0, 1 },  // BMS_Version: DelayTime=5ms (物理量分组), CycleTime=50ms
};

/* Timer initialization */
void can_iahp_bms_timer_init(void)
{
    // 初始化所有消息的定时器
    for (int i = 0; i < 46; i++) {
        can_iahp_bms_msg_timings[i].delay_counter = can_iahp_bms_msg_timings[i].delay_time;
        can_iahp_bms_msg_timings[i].cycle_counter = 0;
    }
}

/* 1ms定时器处理函数 - 实现GenMsgDelayTime延迟 */
void can_iahp_bms_timer_1ms_handler(void)
{
    static uint8_t ms_counter = 0;
    static uint8_t cycle_start = 0;

    ms_counter++;

    // 每50ms开始一个新的发送周期
    if (ms_counter >= 50) {
        ms_counter = 0;
        cycle_start = 1;  // 标记新周期开始
    }

    // 在每个50ms周期内，根据延迟时间分散发送消息
    if (cycle_start || ms_counter <= 5) {  // 只在前6ms内处理延迟发送
        can_iahp_bms_process_delayed_messages(ms_counter);
    }

    cycle_start = 0;  // 清除周期开始标志
}

/* 处理延迟消息发送 - 在每个1ms时刻检查应该发送的消息 */
void can_iahp_bms_process_delayed_messages(uint8_t current_ms)
{
    uint8_t data[8];

    // 遍历所有消息，检查是否应该在当前时刻发送
    for (int i = 0; i < 46; i++) {
        if (can_iahp_bms_msg_timings[i].enabled &&
            can_iahp_bms_msg_timings[i].delay_time == current_ms) {

            // 发送消息
            switch(i) {
                case 0: // BMS_PackInfo1
                {
                    struct can_iahp_bms_bms_packinfo1_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_packinfo1_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000100, data, 8);
                    }
                    break;
                }
                case 1: // BMS_PackInfo2
                {
                    struct can_iahp_bms_bms_packinfo2_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_packinfo2_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000101, data, 8);
                    }
                    break;
                }
                case 2: // BMS_PackInfo3
                {
                    struct can_iahp_bms_bms_packinfo3_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_packinfo3_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000102, data, 8);
                    }
                    break;
                }
                case 3: // BMS_PackInfo4
                {
                    struct can_iahp_bms_bms_packinfo4_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_packinfo4_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000103, data, 8);
                    }
                    break;
                }
                case 4: // BMS_PackInfo5
                {
                    struct can_iahp_bms_bms_packinfo5_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_packinfo5_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000104, data, 8);
                    }
                    break;
                }
                case 5: // BMS_PackInfo6
                {
                    struct can_iahp_bms_bms_packinfo6_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_packinfo6_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000105, data, 8);
                    }
                    break;
                }
                case 6: // BMS_PackInfo7
                {
                    struct can_iahp_bms_bms_packinfo7_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_packinfo7_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000106, data, 8);
                    }
                    break;
                }
                case 7: // BMS_PackInfo8
                {
                    struct can_iahp_bms_bms_packinfo8_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_packinfo8_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000107, data, 8);
                    }
                    break;
                }
                case 8: // BMS_Status1
                {
                    struct can_iahp_bms_bms_status1_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_status1_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000108, data, 8);
                    }
                    break;
                }
                case 9: // BMS_Status2
                {
                    struct can_iahp_bms_bms_status2_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_status2_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000109, data, 8);
                    }
                    break;
                }
                case 10: // BMS_Status3
                {
                    struct can_iahp_bms_bms_status3_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_status3_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000010A, data, 8);
                    }
                    break;
                }
                case 11: // BMS_Status4
                {
                    struct can_iahp_bms_bms_status4_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_status4_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000010B, data, 8);
                    }
                    break;
                }
                case 12: // BMS_Status5
                {
                    struct can_iahp_bms_bms_status5_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_status5_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000010C, data, 8);
                    }
                    break;
                }
                case 13: // BMS_Temp01_04
                {
                    struct can_iahp_bms_bms_temp01_04_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp01_04_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000010D, data, 8);
                    }
                    break;
                }
                case 14: // BMS_Temp05_08
                {
                    struct can_iahp_bms_bms_temp05_08_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp05_08_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000010E, data, 8);
                    }
                    break;
                }
                case 15: // BMS_Temp09_12
                {
                    struct can_iahp_bms_bms_temp09_12_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp09_12_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000010F, data, 8);
                    }
                    break;
                }
                case 16: // BMS_Temp13_16
                {
                    struct can_iahp_bms_bms_temp13_16_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp13_16_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000110, data, 8);
                    }
                    break;
                }
                case 17: // BMS_Temp17_20
                {
                    struct can_iahp_bms_bms_temp17_20_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp17_20_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000111, data, 8);
                    }
                    break;
                }
                case 18: // BMS_Temp21_24
                {
                    struct can_iahp_bms_bms_temp21_24_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp21_24_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000112, data, 8);
                    }
                    break;
                }
                case 19: // BMS_Temp25_28
                {
                    struct can_iahp_bms_bms_temp25_28_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp25_28_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000113, data, 8);
                    }
                    break;
                }
                case 20: // BMS_Temp29_32
                {
                    struct can_iahp_bms_bms_temp29_32_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp29_32_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000114, data, 8);
                    }
                    break;
                }
                case 21: // BMS_Temp33_36
                {
                    struct can_iahp_bms_bms_temp33_36_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp33_36_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000115, data, 8);
                    }
                    break;
                }
                case 22: // BMS_Temp37_40
                {
                    struct can_iahp_bms_bms_temp37_40_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp37_40_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000116, data, 8);
                    }
                    break;
                }
                case 23: // BMS_Temp41_44
                {
                    struct can_iahp_bms_bms_temp41_44_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp41_44_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000117, data, 8);
                    }
                    break;
                }
                case 24: // BMS_Temp45_48
                {
                    struct can_iahp_bms_bms_temp45_48_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_temp45_48_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000118, data, 8);
                    }
                    break;
                }
                case 25: // BMS_Cell01_04
                {
                    struct can_iahp_bms_bms_cell01_04_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell01_04_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000119, data, 8);
                    }
                    break;
                }
                case 26: // BMS_Cell05_08
                {
                    struct can_iahp_bms_bms_cell05_08_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell05_08_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000011A, data, 8);
                    }
                    break;
                }
                case 27: // BMS_Cell09_12
                {
                    struct can_iahp_bms_bms_cell09_12_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell09_12_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000011B, data, 8);
                    }
                    break;
                }
                case 28: // BMS_Cell13_16
                {
                    struct can_iahp_bms_bms_cell13_16_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell13_16_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000011C, data, 8);
                    }
                    break;
                }
                case 29: // BMS_Cell17_20
                {
                    struct can_iahp_bms_bms_cell17_20_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell17_20_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000011D, data, 8);
                    }
                    break;
                }
                case 30: // BMS_Cell21_24
                {
                    struct can_iahp_bms_bms_cell21_24_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell21_24_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000011E, data, 8);
                    }
                    break;
                }
                case 31: // BMS_Cell25_28
                {
                    struct can_iahp_bms_bms_cell25_28_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell25_28_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000011F, data, 8);
                    }
                    break;
                }
                case 32: // BMS_Cell29_32
                {
                    struct can_iahp_bms_bms_cell29_32_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell29_32_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000120, data, 8);
                    }
                    break;
                }
                case 33: // BMS_Cell33_36
                {
                    struct can_iahp_bms_bms_cell33_36_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell33_36_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000121, data, 8);
                    }
                    break;
                }
                case 34: // BMS_Cell37_40
                {
                    struct can_iahp_bms_bms_cell37_40_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell37_40_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000122, data, 8);
                    }
                    break;
                }
                case 35: // BMS_Cell41_44
                {
                    struct can_iahp_bms_bms_cell41_44_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell41_44_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000123, data, 8);
                    }
                    break;
                }
                case 36: // BMS_Cell45_48
                {
                    struct can_iahp_bms_bms_cell45_48_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell45_48_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000124, data, 8);
                    }
                    break;
                }
                case 37: // BMS_Cell49_52
                {
                    struct can_iahp_bms_bms_cell49_52_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell49_52_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000125, data, 8);
                    }
                    break;
                }
                case 38: // BMS_Cell53_56
                {
                    struct can_iahp_bms_bms_cell53_56_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell53_56_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000126, data, 8);
                    }
                    break;
                }
                case 39: // BMS_Cell57_60
                {
                    struct can_iahp_bms_bms_cell57_60_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell57_60_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000127, data, 8);
                    }
                    break;
                }
                case 40: // BMS_Cell61_64
                {
                    struct can_iahp_bms_bms_cell61_64_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell61_64_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000128, data, 8);
                    }
                    break;
                }
                case 41: // BMS_Cell65_68
                {
                    struct can_iahp_bms_bms_cell65_68_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell65_68_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x00000129, data, 8);
                    }
                    break;
                }
                case 42: // BMS_Cell69_72
                {
                    struct can_iahp_bms_bms_cell69_72_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_cell69_72_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000012A, data, 8);
                    }
                    break;
                }
                case 43: // BMS_OtherVolt1
                {
                    struct can_iahp_bms_bms_othervolt1_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_othervolt1_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000012B, data, 8);
                    }
                    break;
                }
                case 44: // BMS_OtherVolt2
                {
                    struct can_iahp_bms_bms_othervolt2_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_othervolt2_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000012C, data, 8);
                    }
                    break;
                }
                case 45: // BMS_Version
                {
                    struct can_iahp_bms_bms_version_t msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if (can_iahp_bms_bms_version_pack(data, &msg_data, sizeof(data)) == 0) {
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x0000012D, data, 8);
                    }
                    break;
                }
            }

            // 重置延迟计数器，准备下一个周期
            can_iahp_bms_msg_timings[i].delay_counter = can_iahp_bms_msg_timings[i].delay_time;
        }
    }
}


/* Pack function for BMS_PackInfo1 - BMS广播数据打包 */
int can_iahp_bms_bms_packinfo1_pack(uint8_t *data, const can_iahp_bms_bms_packinfo1_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_vpack, 0, 32);
    SET_SIGNAL(data, msg->bms_packvoltage, 32, 32);

    return 0;
}

/* Pack function for BMS_PackInfo2 - BMS广播数据打包 */
int can_iahp_bms_bms_packinfo2_pack(uint8_t *data, const can_iahp_bms_bms_packinfo2_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_packcurrent, 0, 32);
    SET_SIGNAL(data, msg->bms_avgcurrent, 32, 32);

    return 0;
}

/* Pack function for BMS_PackInfo3 - BMS广播数据打包 */
int can_iahp_bms_bms_packinfo3_pack(uint8_t *data, const can_iahp_bms_bms_packinfo3_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_rsoc, 0, 16);
    SET_SIGNAL(data, msg->bms_asoc, 16, 16);
    SET_SIGNAL(data, msg->bms_rc, 32, 32);

    return 0;
}

/* Pack function for BMS_PackInfo4 - BMS广播数据打包 */
int can_iahp_bms_bms_packinfo4_pack(uint8_t *data, const can_iahp_bms_bms_packinfo4_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_fcc, 0, 32);
    SET_SIGNAL(data, msg->bms_cyclecount, 32, 16);
    SET_SIGNAL(data, msg->bms_learncycle, 48, 16);

    return 0;
}

/* Pack function for BMS_PackInfo5 - BMS广播数据打包 */
int can_iahp_bms_bms_packinfo5_pack(uint8_t *data, const can_iahp_bms_bms_packinfo5_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_userrc, 0, 32);
    SET_SIGNAL(data, msg->bms_dcr, 32, 32);

    return 0;
}

/* Pack function for BMS_PackInfo6 - BMS广播数据打包 */
int can_iahp_bms_bms_packinfo6_pack(uint8_t *data, const can_iahp_bms_bms_packinfo6_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_fdcr, 0, 32);
    SET_SIGNAL(data, msg->bms_userrsoc, 32, 16);
    SET_SIGNAL(data, msg->bms_fccmin, 48, 16);

    return 0;
}

/* Pack function for BMS_PackInfo7 - BMS广播数据打包 */
int can_iahp_bms_bms_packinfo7_pack(uint8_t *data, const can_iahp_bms_bms_packinfo7_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_deltarc, 0, 32);
    SET_SIGNAL(data, msg->bms_srartrsoc, 32, 16);
    SET_SIGNAL(data, msg->bms_startfdcr, 48, 16);

    return 0;
}

/* Pack function for BMS_PackInfo8 - BMS广播数据打包 */
int can_iahp_bms_bms_packinfo8_pack(uint8_t *data, const can_iahp_bms_bms_packinfo8_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_rcmincutoff, 0, 32);

    return 0;
}

/* Pack function for BMS_Status1 - BMS广播数据打包 */
int can_iahp_bms_bms_status1_pack(uint8_t *data, const can_iahp_bms_bms_status1_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_batterystatuslow, 0, 16);
    SET_SIGNAL(data, msg->bms_batterystatushigh, 16, 16);
    SET_SIGNAL(data, msg->bms_packstatuslow, 32, 16);
    SET_SIGNAL(data, msg->bms_packstatushigh, 48, 16);

    return 0;
}

/* Pack function for BMS_Status2 - BMS广播数据打包 */
int can_iahp_bms_bms_status2_pack(uint8_t *data, const can_iahp_bms_bms_status2_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_safetystatuslow, 0, 16);
    SET_SIGNAL(data, msg->bms_safetystatushigh, 16, 16);
    SET_SIGNAL(data, msg->bms_warnstatus, 32, 16);
    SET_SIGNAL(data, msg->bms_ststatus, 48, 16);

    return 0;
}

/* Pack function for BMS_Status3 - BMS广播数据打包 */
int can_iahp_bms_bms_status3_pack(uint8_t *data, const can_iahp_bms_bms_status3_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_pfstatuslow, 0, 16);
    SET_SIGNAL(data, msg->bms_pfstatushigh, 16, 16);
    SET_SIGNAL(data, msg->bms_cbs0_15, 32, 16);
    SET_SIGNAL(data, msg->bms_startrsocmin, 48, 16);

    return 0;
}

/* Pack function for BMS_Status4 - BMS广播数据打包 */
int can_iahp_bms_bms_status4_pack(uint8_t *data, const can_iahp_bms_bms_status4_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_usagecapacity, 0, 32);
    SET_SIGNAL(data, msg->bms_succchacap, 32, 32);

    return 0;
}

/* Pack function for BMS_Status5 - BMS广播数据打包 */
int can_iahp_bms_bms_status5_pack(uint8_t *data, const can_iahp_bms_bms_status5_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_systemtime, 0, 32);
    SET_SIGNAL(data, msg->bms_engmode, 32, 8);

    return 0;
}

/* Pack function for BMS_Temp01_04 - BMS广播数据打包 */
int can_iahp_bms_bms_temp01_04_pack(uint8_t *data, const can_iahp_bms_bms_temp01_04_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp1, 0, 16);
    SET_SIGNAL(data, msg->bms_temp2, 16, 16);
    SET_SIGNAL(data, msg->bms_temp3, 32, 16);
    SET_SIGNAL(data, msg->bms_temp4, 48, 16);

    return 0;
}

/* Pack function for BMS_Temp05_08 - BMS广播数据打包 */
int can_iahp_bms_bms_temp05_08_pack(uint8_t *data, const can_iahp_bms_bms_temp05_08_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp5, 0, 16);
    SET_SIGNAL(data, msg->bms_temp6, 16, 16);
    SET_SIGNAL(data, msg->bms_temp7, 32, 16);
    SET_SIGNAL(data, msg->bms_temp8, 48, 16);

    return 0;
}

/* Pack function for BMS_Temp09_12 - BMS广播数据打包 */
int can_iahp_bms_bms_temp09_12_pack(uint8_t *data, const can_iahp_bms_bms_temp09_12_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp9, 0, 16);
    SET_SIGNAL(data, msg->bms_temp10, 16, 16);
    SET_SIGNAL(data, msg->bms_temp11, 32, 16);
    SET_SIGNAL(data, msg->bms_temp12, 48, 16);

    return 0;
}

/* Pack function for BMS_Temp13_16 - BMS广播数据打包 */
int can_iahp_bms_bms_temp13_16_pack(uint8_t *data, const can_iahp_bms_bms_temp13_16_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp13, 0, 16);
    SET_SIGNAL(data, msg->bms_temp14, 16, 16);
    SET_SIGNAL(data, msg->bms_temp15, 32, 16);
    SET_SIGNAL(data, msg->bms_temp16, 48, 16);

    return 0;
}

/* Pack function for BMS_Temp17_20 - BMS广播数据打包 */
int can_iahp_bms_bms_temp17_20_pack(uint8_t *data, const can_iahp_bms_bms_temp17_20_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp17, 0, 16);
    SET_SIGNAL(data, msg->bms_temp18, 16, 16);
    SET_SIGNAL(data, msg->bms_temp19, 32, 16);
    SET_SIGNAL(data, msg->bms_temp20, 48, 16);

    return 0;
}

/* Pack function for BMS_Temp21_24 - BMS广播数据打包 */
int can_iahp_bms_bms_temp21_24_pack(uint8_t *data, const can_iahp_bms_bms_temp21_24_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp21, 0, 16);
    SET_SIGNAL(data, msg->bms_temp22, 16, 16);
    SET_SIGNAL(data, msg->bms_temp23, 32, 16);
    SET_SIGNAL(data, msg->bms_temp24, 48, 16);

    return 0;
}

/* Pack function for BMS_Temp25_28 - BMS广播数据打包 */
int can_iahp_bms_bms_temp25_28_pack(uint8_t *data, const can_iahp_bms_bms_temp25_28_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp25, 0, 16);
    SET_SIGNAL(data, msg->bms_temp26, 16, 16);
    SET_SIGNAL(data, msg->bms_temp27, 32, 16);
    SET_SIGNAL(data, msg->bms_temp28, 48, 16);

    return 0;
}

/* Pack function for BMS_Temp29_32 - BMS广播数据打包 */
int can_iahp_bms_bms_temp29_32_pack(uint8_t *data, const can_iahp_bms_bms_temp29_32_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp29, 0, 16);
    SET_SIGNAL(data, msg->bms_temp30, 16, 16);
    SET_SIGNAL(data, msg->bms_temp31, 32, 16);
    SET_SIGNAL(data, msg->bms_temp32, 48, 16);

    return 0;
}

/* Pack function for BMS_Temp33_36 - BMS广播数据打包 */
int can_iahp_bms_bms_temp33_36_pack(uint8_t *data, const can_iahp_bms_bms_temp33_36_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp33, 0, 16);
    SET_SIGNAL(data, msg->bms_temp34, 16, 16);
    SET_SIGNAL(data, msg->bms_temp35, 32, 16);
    SET_SIGNAL(data, msg->bms_temp36, 48, 16);

    return 0;
}

/* Pack function for BMS_Temp37_40 - BMS广播数据打包 */
int can_iahp_bms_bms_temp37_40_pack(uint8_t *data, const can_iahp_bms_bms_temp37_40_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp37, 0, 16);
    SET_SIGNAL(data, msg->bms_temp38, 16, 16);
    SET_SIGNAL(data, msg->bms_temp39, 32, 16);
    SET_SIGNAL(data, msg->bms_temp40, 48, 16);

    return 0;
}

/* Pack function for BMS_Temp41_44 - BMS广播数据打包 */
int can_iahp_bms_bms_temp41_44_pack(uint8_t *data, const can_iahp_bms_bms_temp41_44_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp41, 0, 16);
    SET_SIGNAL(data, msg->bms_temp42, 16, 16);
    SET_SIGNAL(data, msg->bms_temp43, 32, 16);
    SET_SIGNAL(data, msg->bms_temp44, 48, 16);

    return 0;
}

/* Pack function for BMS_Temp45_48 - BMS广播数据打包 */
int can_iahp_bms_bms_temp45_48_pack(uint8_t *data, const can_iahp_bms_bms_temp45_48_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_temp45, 0, 16);
    SET_SIGNAL(data, msg->bms_temp46, 16, 16);
    SET_SIGNAL(data, msg->bms_temp47, 32, 16);
    SET_SIGNAL(data, msg->bms_temp48, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell01_04 - BMS广播数据打包 */
int can_iahp_bms_bms_cell01_04_pack(uint8_t *data, const can_iahp_bms_bms_cell01_04_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt1, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt2, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt3, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt4, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell05_08 - BMS广播数据打包 */
int can_iahp_bms_bms_cell05_08_pack(uint8_t *data, const can_iahp_bms_bms_cell05_08_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt5, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt6, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt7, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt8, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell09_12 - BMS广播数据打包 */
int can_iahp_bms_bms_cell09_12_pack(uint8_t *data, const can_iahp_bms_bms_cell09_12_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt9, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt10, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt11, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt12, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell13_16 - BMS广播数据打包 */
int can_iahp_bms_bms_cell13_16_pack(uint8_t *data, const can_iahp_bms_bms_cell13_16_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt13, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt14, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt15, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt16, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell17_20 - BMS广播数据打包 */
int can_iahp_bms_bms_cell17_20_pack(uint8_t *data, const can_iahp_bms_bms_cell17_20_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt17, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt18, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt19, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt20, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell21_24 - BMS广播数据打包 */
int can_iahp_bms_bms_cell21_24_pack(uint8_t *data, const can_iahp_bms_bms_cell21_24_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt21, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt22, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt23, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt24, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell25_28 - BMS广播数据打包 */
int can_iahp_bms_bms_cell25_28_pack(uint8_t *data, const can_iahp_bms_bms_cell25_28_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt25, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt26, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt27, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt28, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell29_32 - BMS广播数据打包 */
int can_iahp_bms_bms_cell29_32_pack(uint8_t *data, const can_iahp_bms_bms_cell29_32_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt29, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt30, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt31, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt32, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell33_36 - BMS广播数据打包 */
int can_iahp_bms_bms_cell33_36_pack(uint8_t *data, const can_iahp_bms_bms_cell33_36_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt33, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt34, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt35, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt36, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell37_40 - BMS广播数据打包 */
int can_iahp_bms_bms_cell37_40_pack(uint8_t *data, const can_iahp_bms_bms_cell37_40_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt37, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt38, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt39, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt40, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell41_44 - BMS广播数据打包 */
int can_iahp_bms_bms_cell41_44_pack(uint8_t *data, const can_iahp_bms_bms_cell41_44_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt41, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt42, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt43, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt44, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell45_48 - BMS广播数据打包 */
int can_iahp_bms_bms_cell45_48_pack(uint8_t *data, const can_iahp_bms_bms_cell45_48_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt45, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt46, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt47, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt48, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell49_52 - BMS广播数据打包 */
int can_iahp_bms_bms_cell49_52_pack(uint8_t *data, const can_iahp_bms_bms_cell49_52_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt49, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt50, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt51, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt52, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell53_56 - BMS广播数据打包 */
int can_iahp_bms_bms_cell53_56_pack(uint8_t *data, const can_iahp_bms_bms_cell53_56_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt53, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt54, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt55, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt56, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell57_60 - BMS广播数据打包 */
int can_iahp_bms_bms_cell57_60_pack(uint8_t *data, const can_iahp_bms_bms_cell57_60_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt57, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt58, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt59, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt60, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell61_64 - BMS广播数据打包 */
int can_iahp_bms_bms_cell61_64_pack(uint8_t *data, const can_iahp_bms_bms_cell61_64_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt61, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt62, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt63, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt64, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell65_68 - BMS广播数据打包 */
int can_iahp_bms_bms_cell65_68_pack(uint8_t *data, const can_iahp_bms_bms_cell65_68_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt65, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt66, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt67, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt68, 48, 16);

    return 0;
}

/* Pack function for BMS_Cell69_72 - BMS广播数据打包 */
int can_iahp_bms_bms_cell69_72_pack(uint8_t *data, const can_iahp_bms_bms_cell69_72_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_cellvolt69, 0, 16);
    SET_SIGNAL(data, msg->bms_cellvolt70, 16, 16);
    SET_SIGNAL(data, msg->bms_cellvolt71, 32, 16);
    SET_SIGNAL(data, msg->bms_cellvolt72, 48, 16);

    return 0;
}

/* Pack function for BMS_OtherVolt1 - BMS广播数据打包 */
int can_iahp_bms_bms_othervolt1_pack(uint8_t *data, const can_iahp_bms_bms_othervolt1_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_vdfuse, 0, 32);
    SET_SIGNAL(data, msg->bms_vchgplus, 32, 32);

    return 0;
}

/* Pack function for BMS_OtherVolt2 - BMS广播数据打包 */
int can_iahp_bms_bms_othervolt2_pack(uint8_t *data, const can_iahp_bms_bms_othervolt2_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_vpackplus, 0, 32);
    SET_SIGNAL(data, msg->bms_vcfuse, 32, 32);

    return 0;
}

/* Pack function for BMS_Version - BMS广播数据打包 */
int can_iahp_bms_bms_version_pack(uint8_t *data, const can_iahp_bms_bms_version_t *msg, uint8_t size)
{
    if (data == NULL || msg == NULL || size < 8) {
        return -1;
    }

    memset(data, 0, 8);

    SET_SIGNAL(data, msg->bms_chksum, 0, 32);
    SET_SIGNAL(data, msg->bms_publicver, 32, 24);

    return 0;
}
