# CAN_IAHP_BMS 消息信息

## 消息统计
- 总消息数: 46
- 扩展帧数: 46
- 标准帧数: 0

## 消息列表

| 消息名称 | CAN ID | 帧类型 | 长度 | 周期时间 | 信号数 |
|----------|--------|--------|------|----------|--------|
| BMS_PackInfo1 | 0x00000100 | 扩展帧 | 8 | 50ms | 2 |
| BMS_PackInfo2 | 0x00000101 | 扩展帧 | 8 | 50ms | 2 |
| BMS_PackInfo3 | 0x00000102 | 扩展帧 | 8 | 50ms | 3 |
| BMS_PackInfo4 | 0x00000103 | 扩展帧 | 8 | 50ms | 3 |
| BMS_PackInfo5 | 0x00000104 | 扩展帧 | 8 | 50ms | 2 |
| BMS_PackInfo6 | 0x00000105 | 扩展帧 | 8 | 50ms | 3 |
| BMS_PackInfo7 | 0x00000106 | 扩展帧 | 8 | 50ms | 3 |
| BMS_PackInfo8 | 0x00000107 | 扩展帧 | 8 | 50ms | 1 |
| BMS_Status1 | 0x00000108 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Status2 | 0x00000109 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Status3 | 0x0000010A | 扩展帧 | 8 | 50ms | 4 |
| BMS_Status4 | 0x0000010B | 扩展帧 | 8 | 50ms | 2 |
| BMS_Status5 | 0x0000010C | 扩展帧 | 8 | 50ms | 2 |
| BMS_Temp01_04 | 0x0000010D | 扩展帧 | 8 | 50ms | 4 |
| BMS_Temp05_08 | 0x0000010E | 扩展帧 | 8 | 50ms | 4 |
| BMS_Temp09_12 | 0x0000010F | 扩展帧 | 8 | 50ms | 4 |
| BMS_Temp13_16 | 0x00000110 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Temp17_20 | 0x00000111 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Temp21_24 | 0x00000112 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Temp25_28 | 0x00000113 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Temp29_32 | 0x00000114 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Temp33_36 | 0x00000115 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Temp37_40 | 0x00000116 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Temp41_44 | 0x00000117 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Temp45_48 | 0x00000118 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell01_04 | 0x00000119 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell05_08 | 0x0000011A | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell09_12 | 0x0000011B | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell13_16 | 0x0000011C | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell17_20 | 0x0000011D | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell21_24 | 0x0000011E | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell25_28 | 0x0000011F | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell29_32 | 0x00000120 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell33_36 | 0x00000121 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell37_40 | 0x00000122 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell41_44 | 0x00000123 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell45_48 | 0x00000124 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell49_52 | 0x00000125 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell53_56 | 0x00000126 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell57_60 | 0x00000127 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell61_64 | 0x00000128 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell65_68 | 0x00000129 | 扩展帧 | 8 | 50ms | 4 |
| BMS_Cell69_72 | 0x0000012A | 扩展帧 | 8 | 50ms | 4 |
| BMS_OtherVolt1 | 0x0000012B | 扩展帧 | 8 | 50ms | 2 |
| BMS_OtherVolt2 | 0x0000012C | 扩展帧 | 8 | 50ms | 2 |
| BMS_Version | 0x0000012D | 扩展帧 | 8 | 50ms | 2 |

## 信号统计
- 总信号数: 161

## 使用说明

### 包含头文件
```c
#include "can_iahp_bms.h"
```

### BMS端：打包并广播消息
```c
uint8_t data[8];
struct can_iahp_bms_bms_packinfo1_t msg;

// 设置BMS数据
msg.bms_vpack = 3600;        // 电池包电压 (0.01V)
msg.bms_packvoltage = 3600;  // 包电压 (0.01V)

// 打包消息
can_iahp_bms_bms_packinfo1_pack(data, &msg, sizeof(data));

// 广播到CAN总线
can_broadcast(CAN_IAHP_BMS_BMS_PACKINFO1_ID, data, 8);
```

### 重要说明
- **BMS专用代码**：只包含pack函数，用于数据打包和广播
- **无接收功能**：BMS系统不需要接收其他消息，因此无unpack函数
- **广播模式**：BMS作为唯一发送方，向CAN总线广播数据
- **50ms周期**：延迟时间0~5ms分散发送，避免总线拥塞
- **扩展帧格式**：29位CAN ID，符合汽车CAN标准

### 其他ECU接收说明
其他ECU如需接收BMS广播数据，请：
1. 使用相同的DBC文件生成对应的unpack函数
2. 或根据信号定义手动实现数据解析
3. 本代码专注于BMS端功能，不包含接收相关代码
