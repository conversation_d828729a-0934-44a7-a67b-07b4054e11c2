/*
 * CAN_IAHP_BMS 使用示例
 * BMS广播消息处理示例代码 - 修正版
 *
 * 特性：
 * - 基于GenMsgDelayTime的1ms精度延迟分散发送
 * - 物理量分组的智能延迟策略
 * - 真正的负载分散，避免总线拥塞
 *
 * 注意：BMS系统中所有消息都是广播消息
 * - BMS作为发送方：打包并广播数据
 * - 其他ECU作为接收方：接收并解包数据
 */

#include "can_iahp_bms.h"
#include <string.h>

/* 全局变量：模拟BMS数据 */
typedef struct {
    float pack_voltage;      // 包电压 V
    float pack_current;      // 包电流 A
    float soc;              // SOC %
    float soh;              // SOH %
    float max_cell_voltage; // 最高单体电压 V
    float min_cell_voltage; // 最低单体电压 V
    float max_temp;         // 最高温度 °C
    float min_temp;         // 最低温度 °C
    uint32_t system_status; // 系统状态
    uint32_t fault_status;  // 故障状态
} BMS_Data_t;

static BMS_Data_t bms_data = {
    .pack_voltage = 36.5f,
    .pack_current = 10.2f,
    .soc = 85.5f,
    .soh = 98.2f,
    .max_cell_voltage = 3.65f,
    .min_cell_voltage = 3.62f,
    .max_temp = 25.8f,
    .min_temp = 23.2f,
    .system_status = 0x00000001,  // 正常状态
    .fault_status = 0x00000000    // 无故障
};

/* 函数声明 */
void bms_manual_broadcast_example(void);
void bms_timer_broadcast_example(void);
void can_broadcast(uint32_t can_id, uint8_t *data, uint8_t length);
void TIM_1ms_IRQHandler(void);

/* BMS端：手动打包并发送广播消息示例 - 全部46个消息 */
void bms_manual_broadcast_example(void)
{
    uint8_t data[8];

    // ========== 0ms延迟 - PackInfo消息组 (8个消息) ==========

    /* BMS_PackInfo1 */
    {
        struct can_iahp_bms_bms_packinfo1_t msg;
        msg.bms_vpack = (uint32_t)(bms_data.pack_voltage * 100);
        msg.bms_packvoltage = (uint32_t)(bms_data.pack_voltage * 100);
        can_iahp_bms_bms_packinfo1_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_PACKINFO1_ID, data, 8);
    }

    /* BMS_PackInfo2 */
    {
        struct can_iahp_bms_bms_packinfo2_t msg;
        msg.bms_packcurrent = (int32_t)(bms_data.pack_current * 100);
        msg.bms_avgcurrent = (int32_t)(bms_data.pack_current * 100);
        can_iahp_bms_bms_packinfo2_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_PACKINFO2_ID, data, 8);
    }

    /* BMS_PackInfo3 */
    {
        struct can_iahp_bms_bms_packinfo3_t msg;
        msg.bms_rsoc = (uint16_t)(bms_data.soc * 100);
        msg.bms_asoc = (uint16_t)(bms_data.soc * 100);
        can_iahp_bms_bms_packinfo3_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_PACKINFO3_ID, data, 8);
    }

    /* BMS_PackInfo4 */
    {
        struct can_iahp_bms_bms_packinfo4_t msg;
        msg.bms_soh = (uint16_t)(bms_data.soh * 100);
        msg.bms_maxcellvoltage = (uint16_t)(bms_data.max_cell_voltage * 1000);
        can_iahp_bms_bms_packinfo4_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_PACKINFO4_ID, data, 8);
    }

    /* BMS_PackInfo5 */
    {
        struct can_iahp_bms_bms_packinfo5_t msg;
        msg.bms_mincellvoltage = (uint16_t)(bms_data.min_cell_voltage * 1000);
        msg.bms_avgcellvoltage = (uint16_t)((bms_data.max_cell_voltage + bms_data.min_cell_voltage) / 2 * 1000);
        can_iahp_bms_bms_packinfo5_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_PACKINFO5_ID, data, 8);
    }

    /* BMS_PackInfo6 */
    {
        struct can_iahp_bms_bms_packinfo6_t msg;
        msg.bms_maxcelltemp = (int16_t)(bms_data.max_temp * 10);
        msg.bms_mincelltemp = (int16_t)(bms_data.min_temp * 10);
        can_iahp_bms_bms_packinfo6_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_PACKINFO6_ID, data, 8);
    }

    /* BMS_PackInfo7 */
    {
        struct can_iahp_bms_bms_packinfo7_t msg;
        msg.bms_avgcelltemp = (int16_t)((bms_data.max_temp + bms_data.min_temp) / 2 * 10);
        msg.bms_mostemperature = (int16_t)(35.0f * 10); // 示例MOS温度
        can_iahp_bms_bms_packinfo7_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_PACKINFO7_ID, data, 8);
    }

    /* BMS_PackInfo8 */
    {
        struct can_iahp_bms_bms_packinfo8_t msg;
        msg.bms_envtemperature = (int16_t)(22.0f * 10); // 示例环境温度
        msg.bms_remaincapacity = (uint32_t)(50.5f * 100); // 示例剩余容量
        can_iahp_bms_bms_packinfo8_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_PACKINFO8_ID, data, 8);
    }

    // ========== 1ms延迟 - Status消息组 (5个消息) ==========

    /* BMS_Status1 */
    {
        struct can_iahp_bms_bms_status1_t msg;
        msg.bms_systemflag1 = bms_data.system_status;
        msg.bms_systemflag2 = bms_data.fault_status;
        can_iahp_bms_bms_status1_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_STATUS1_ID, data, 8);
    }

    /* BMS_Status2 */
    {
        struct can_iahp_bms_bms_status2_t msg;
        msg.bms_chargestatus = 1; // 充电状态
        msg.bms_dischargestatus = 0; // 放电状态
        can_iahp_bms_bms_status2_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_STATUS2_ID, data, 8);
    }

    /* BMS_Status3 */
    {
        struct can_iahp_bms_bms_status3_t msg;
        msg.bms_balancestatus1 = 0x12345678; // 平衡状态低32位
        msg.bms_balancestatus2 = 0x9ABCDEF0; // 平衡状态高32位
        can_iahp_bms_bms_status3_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_STATUS3_ID, data, 8);
    }

    /* BMS_Status4 */
    {
        struct can_iahp_bms_bms_status4_t msg;
        msg.bms_relaystatus = 0x0F; // 继电器状态
        msg.bms_faultstatus = bms_data.fault_status;
        can_iahp_bms_bms_status4_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_STATUS4_ID, data, 8);
    }

    /* BMS_Status5 */
    {
        struct can_iahp_bms_bms_status5_t msg;
        msg.bms_warningstatus = 0x00000000; // 警告状态
        msg.bms_protectionstatus = 0x00000000; // 保护状态
        can_iahp_bms_bms_status5_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_STATUS5_ID, data, 8);
    }

    // ========== 3ms延迟 - Temp消息组 (12个消息) ==========

    /* BMS_Temp01_04 */
    {
        struct can_iahp_bms_bms_temp01_04_t msg;
        msg.bms_temp01 = (int16_t)(bms_data.max_temp * 10);
        msg.bms_temp02 = (int16_t)(bms_data.min_temp * 10);
        msg.bms_temp03 = (int16_t)(24.5f * 10);
        msg.bms_temp04 = (int16_t)(25.2f * 10);
        can_iahp_bms_bms_temp01_04_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP01_04_ID, data, 8);
    }

    /* BMS_Temp05_08 */
    {
        struct can_iahp_bms_bms_temp05_08_t msg;
        msg.bms_temp05 = (int16_t)(23.8f * 10);
        msg.bms_temp06 = (int16_t)(24.1f * 10);
        msg.bms_temp07 = (int16_t)(24.7f * 10);
        msg.bms_temp08 = (int16_t)(25.0f * 10);
        can_iahp_bms_bms_temp05_08_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP05_08_ID, data, 8);
    }

    /* BMS_Temp09_12 */
    {
        struct can_iahp_bms_bms_temp09_12_t msg;
        msg.bms_temp09 = (int16_t)(24.3f * 10);
        msg.bms_temp10 = (int16_t)(24.6f * 10);
        msg.bms_temp11 = (int16_t)(24.9f * 10);
        msg.bms_temp12 = (int16_t)(25.1f * 10);
        can_iahp_bms_bms_temp09_12_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP09_12_ID, data, 8);
    }

    /* BMS_Temp13_16 */
    {
        struct can_iahp_bms_bms_temp13_16_t msg;
        msg.bms_temp13 = (int16_t)(24.4f * 10);
        msg.bms_temp14 = (int16_t)(24.8f * 10);
        msg.bms_temp15 = (int16_t)(25.3f * 10);
        msg.bms_temp16 = (int16_t)(25.6f * 10);
        can_iahp_bms_bms_temp13_16_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP13_16_ID, data, 8);
    }

    /* BMS_Temp17_20 */
    {
        struct can_iahp_bms_bms_temp17_20_t msg;
        msg.bms_temp17 = (int16_t)(24.2f * 10);
        msg.bms_temp18 = (int16_t)(24.5f * 10);
        msg.bms_temp19 = (int16_t)(24.8f * 10);
        msg.bms_temp20 = (int16_t)(25.1f * 10);
        can_iahp_bms_bms_temp17_20_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP17_20_ID, data, 8);
    }

    /* BMS_Temp21_24 */
    {
        struct can_iahp_bms_bms_temp21_24_t msg;
        msg.bms_temp21 = (int16_t)(24.7f * 10);
        msg.bms_temp22 = (int16_t)(25.0f * 10);
        msg.bms_temp23 = (int16_t)(25.3f * 10);
        msg.bms_temp24 = (int16_t)(25.6f * 10);
        can_iahp_bms_bms_temp21_24_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP21_24_ID, data, 8);
    }

    /* BMS_Temp25_28 */
    {
        struct can_iahp_bms_bms_temp25_28_t msg;
        msg.bms_temp25 = (int16_t)(24.1f * 10);
        msg.bms_temp26 = (int16_t)(24.4f * 10);
        msg.bms_temp27 = (int16_t)(24.7f * 10);
        msg.bms_temp28 = (int16_t)(25.0f * 10);
        can_iahp_bms_bms_temp25_28_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP25_28_ID, data, 8);
    }

    /* BMS_Temp29_32 */
    {
        struct can_iahp_bms_bms_temp29_32_t msg;
        msg.bms_temp29 = (int16_t)(24.6f * 10);
        msg.bms_temp30 = (int16_t)(24.9f * 10);
        msg.bms_temp31 = (int16_t)(25.2f * 10);
        msg.bms_temp32 = (int16_t)(25.5f * 10);
        can_iahp_bms_bms_temp29_32_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP29_32_ID, data, 8);
    }

    /* BMS_Temp33_36 */
    {
        struct can_iahp_bms_bms_temp33_36_t msg;
        msg.bms_temp33 = (int16_t)(24.3f * 10);
        msg.bms_temp34 = (int16_t)(24.6f * 10);
        msg.bms_temp35 = (int16_t)(24.9f * 10);
        msg.bms_temp36 = (int16_t)(25.2f * 10);
        can_iahp_bms_bms_temp33_36_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP33_36_ID, data, 8);
    }

    /* BMS_Temp37_40 */
    {
        struct can_iahp_bms_bms_temp37_40_t msg;
        msg.bms_temp37 = (int16_t)(24.8f * 10);
        msg.bms_temp38 = (int16_t)(25.1f * 10);
        msg.bms_temp39 = (int16_t)(25.4f * 10);
        msg.bms_temp40 = (int16_t)(25.7f * 10);
        can_iahp_bms_bms_temp37_40_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP37_40_ID, data, 8);
    }

    /* BMS_Temp41_44 */
    {
        struct can_iahp_bms_bms_temp41_44_t msg;
        msg.bms_temp41 = (int16_t)(24.5f * 10);
        msg.bms_temp42 = (int16_t)(24.8f * 10);
        msg.bms_temp43 = (int16_t)(25.1f * 10);
        msg.bms_temp44 = (int16_t)(25.4f * 10);
        can_iahp_bms_bms_temp41_44_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP41_44_ID, data, 8);
    }

    /* BMS_Temp45_48 */
    {
        struct can_iahp_bms_bms_temp45_48_t msg;
        msg.bms_temp45 = (int16_t)(25.0f * 10);
        msg.bms_temp46 = (int16_t)(25.3f * 10);
        msg.bms_temp47 = (int16_t)(25.6f * 10);
        msg.bms_temp48 = (int16_t)(25.9f * 10);
        can_iahp_bms_bms_temp45_48_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_TEMP45_48_ID, data, 8);
    }

    // ========== 2ms延迟 - Cell消息组 (18个消息) ==========

    /* BMS_Cell01_04 */
    {
        struct can_iahp_bms_bms_cell01_04_t msg;
        msg.bms_cellvoltage01 = (uint16_t)(3650); // 3.650V
        msg.bms_cellvoltage02 = (uint16_t)(3620); // 3.620V
        msg.bms_cellvoltage03 = (uint16_t)(3630); // 3.630V
        msg.bms_cellvoltage04 = (uint16_t)(3640); // 3.640V
        can_iahp_bms_bms_cell01_04_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL01_04_ID, data, 8);
    }

    /* BMS_Cell05_08 */
    {
        struct can_iahp_bms_bms_cell05_08_t msg;
        msg.bms_cellvoltage05 = (uint16_t)(3635);
        msg.bms_cellvoltage06 = (uint16_t)(3625);
        msg.bms_cellvoltage07 = (uint16_t)(3645);
        msg.bms_cellvoltage08 = (uint16_t)(3655);
        can_iahp_bms_bms_cell05_08_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL05_08_ID, data, 8);
    }

    /* BMS_Cell09_12 */
    {
        struct can_iahp_bms_bms_cell09_12_t msg;
        msg.bms_cellvoltage09 = (uint16_t)(3628);
        msg.bms_cellvoltage10 = (uint16_t)(3638);
        msg.bms_cellvoltage11 = (uint16_t)(3648);
        msg.bms_cellvoltage12 = (uint16_t)(3658);
        can_iahp_bms_bms_cell09_12_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL09_12_ID, data, 8);
    }

    /* BMS_Cell13_16 */
    {
        struct can_iahp_bms_bms_cell13_16_t msg;
        msg.bms_cellvoltage13 = (uint16_t)(3632);
        msg.bms_cellvoltage14 = (uint16_t)(3642);
        msg.bms_cellvoltage15 = (uint16_t)(3652);
        msg.bms_cellvoltage16 = (uint16_t)(3662);
        can_iahp_bms_bms_cell13_16_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL13_16_ID, data, 8);
    }

    /* BMS_Cell17_20 */
    {
        struct can_iahp_bms_bms_cell17_20_t msg;
        msg.bms_cellvoltage17 = (uint16_t)(3626);
        msg.bms_cellvoltage18 = (uint16_t)(3636);
        msg.bms_cellvoltage19 = (uint16_t)(3646);
        msg.bms_cellvoltage20 = (uint16_t)(3656);
        can_iahp_bms_bms_cell17_20_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL17_20_ID, data, 8);
    }

    /* BMS_Cell21_24 */
    {
        struct can_iahp_bms_bms_cell21_24_t msg;
        msg.bms_cellvoltage21 = (uint16_t)(3634);
        msg.bms_cellvoltage22 = (uint16_t)(3644);
        msg.bms_cellvoltage23 = (uint16_t)(3654);
        msg.bms_cellvoltage24 = (uint16_t)(3664);
        can_iahp_bms_bms_cell21_24_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL21_24_ID, data, 8);
    }

    /* BMS_Cell25_28 */
    {
        struct can_iahp_bms_bms_cell25_28_t msg;
        msg.bms_cellvoltage25 = (uint16_t)(3629);
        msg.bms_cellvoltage26 = (uint16_t)(3639);
        msg.bms_cellvoltage27 = (uint16_t)(3649);
        msg.bms_cellvoltage28 = (uint16_t)(3659);
        can_iahp_bms_bms_cell25_28_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL25_28_ID, data, 8);
    }

    /* BMS_Cell29_32 */
    {
        struct can_iahp_bms_bms_cell29_32_t msg;
        msg.bms_cellvoltage29 = (uint16_t)(3633);
        msg.bms_cellvoltage30 = (uint16_t)(3643);
        msg.bms_cellvoltage31 = (uint16_t)(3653);
        msg.bms_cellvoltage32 = (uint16_t)(3663);
        can_iahp_bms_bms_cell29_32_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL29_32_ID, data, 8);
    }

    /* BMS_Cell33_36 */
    {
        struct can_iahp_bms_bms_cell33_36_t msg;
        msg.bms_cellvoltage33 = (uint16_t)(3627);
        msg.bms_cellvoltage34 = (uint16_t)(3637);
        msg.bms_cellvoltage35 = (uint16_t)(3647);
        msg.bms_cellvoltage36 = (uint16_t)(3657);
        can_iahp_bms_bms_cell33_36_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL33_36_ID, data, 8);
    }

    /* BMS_Cell37_40 */
    {
        struct can_iahp_bms_bms_cell37_40_t msg;
        msg.bms_cellvoltage37 = (uint16_t)(3631);
        msg.bms_cellvoltage38 = (uint16_t)(3641);
        msg.bms_cellvoltage39 = (uint16_t)(3651);
        msg.bms_cellvoltage40 = (uint16_t)(3661);
        can_iahp_bms_bms_cell37_40_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL37_40_ID, data, 8);
    }

    /* BMS_Cell41_44 */
    {
        struct can_iahp_bms_bms_cell41_44_t msg;
        msg.bms_cellvoltage41 = (uint16_t)(3624);
        msg.bms_cellvoltage42 = (uint16_t)(3634);
        msg.bms_cellvoltage43 = (uint16_t)(3644);
        msg.bms_cellvoltage44 = (uint16_t)(3654);
        can_iahp_bms_bms_cell41_44_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL41_44_ID, data, 8);
    }

    /* BMS_Cell45_48 */
    {
        struct can_iahp_bms_bms_cell45_48_t msg;
        msg.bms_cellvoltage45 = (uint16_t)(3628);
        msg.bms_cellvoltage46 = (uint16_t)(3638);
        msg.bms_cellvoltage47 = (uint16_t)(3648);
        msg.bms_cellvoltage48 = (uint16_t)(3658);
        can_iahp_bms_bms_cell45_48_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL45_48_ID, data, 8);
    }

    /* BMS_Cell49_52 */
    {
        struct can_iahp_bms_bms_cell49_52_t msg;
        msg.bms_cellvoltage49 = (uint16_t)(3632);
        msg.bms_cellvoltage50 = (uint16_t)(3642);
        msg.bms_cellvoltage51 = (uint16_t)(3652);
        msg.bms_cellvoltage52 = (uint16_t)(3662);
        can_iahp_bms_bms_cell49_52_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL49_52_ID, data, 8);
    }

    /* BMS_Cell53_56 */
    {
        struct can_iahp_bms_bms_cell53_56_t msg;
        msg.bms_cellvoltage53 = (uint16_t)(3626);
        msg.bms_cellvoltage54 = (uint16_t)(3636);
        msg.bms_cellvoltage55 = (uint16_t)(3646);
        msg.bms_cellvoltage56 = (uint16_t)(3656);
        can_iahp_bms_bms_cell53_56_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL53_56_ID, data, 8);
    }

    /* BMS_Cell57_60 */
    {
        struct can_iahp_bms_bms_cell57_60_t msg;
        msg.bms_cellvoltage57 = (uint16_t)(3630);
        msg.bms_cellvoltage58 = (uint16_t)(3640);
        msg.bms_cellvoltage59 = (uint16_t)(3650);
        msg.bms_cellvoltage60 = (uint16_t)(3660);
        can_iahp_bms_bms_cell57_60_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL57_60_ID, data, 8);
    }

    /* BMS_Cell61_64 */
    {
        struct can_iahp_bms_bms_cell61_64_t msg;
        msg.bms_cellvoltage61 = (uint16_t)(3625);
        msg.bms_cellvoltage62 = (uint16_t)(3635);
        msg.bms_cellvoltage63 = (uint16_t)(3645);
        msg.bms_cellvoltage64 = (uint16_t)(3655);
        can_iahp_bms_bms_cell61_64_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL61_64_ID, data, 8);
    }

    /* BMS_Cell65_68 */
    {
        struct can_iahp_bms_bms_cell65_68_t msg;
        msg.bms_cellvoltage65 = (uint16_t)(3629);
        msg.bms_cellvoltage66 = (uint16_t)(3639);
        msg.bms_cellvoltage67 = (uint16_t)(3649);
        msg.bms_cellvoltage68 = (uint16_t)(3659);
        can_iahp_bms_bms_cell65_68_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL65_68_ID, data, 8);
    }

    /* BMS_Cell69_72 */
    {
        struct can_iahp_bms_bms_cell69_72_t msg;
        msg.bms_cellvoltage69 = (uint16_t)(3633);
        msg.bms_cellvoltage70 = (uint16_t)(3643);
        msg.bms_cellvoltage71 = (uint16_t)(3653);
        msg.bms_cellvoltage72 = (uint16_t)(3663);
        can_iahp_bms_bms_cell69_72_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_CELL69_72_ID, data, 8);
    }

    // ========== 4ms延迟 - OtherVolt消息组 (2个消息) ==========

    /* BMS_OtherVolt1 */
    {
        struct can_iahp_bms_bms_othervolt1_t msg;
        msg.bms_busvoltage = (uint16_t)(12500); // 12.5V 总线电压
        msg.bms_5vvoltage = (uint16_t)(5000);   // 5.0V 电源电压
        can_iahp_bms_bms_othervolt1_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_OTHERVOLT1_ID, data, 8);
    }

    /* BMS_OtherVolt2 */
    {
        struct can_iahp_bms_bms_othervolt2_t msg;
        msg.bms_3v3voltage = (uint16_t)(3300);  // 3.3V 电源电压
        msg.bms_refvoltage = (uint16_t)(2500);  // 2.5V 参考电压
        can_iahp_bms_bms_othervolt2_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_OTHERVOLT2_ID, data, 8);
    }

    // ========== 5ms延迟 - Version消息组 (1个消息) ==========

    /* BMS_Version */
    {
        struct can_iahp_bms_bms_version_t msg;
        msg.bms_chksum = 0x12345678;      // 校验和
        msg.bms_publicver = 0x010203;     // 版本号 v1.2.3
        can_iahp_bms_bms_version_pack(data, &msg, sizeof(data));
        can_broadcast(CAN_IAHP_BMS_BMS_VERSION_ID, data, 8);
    }

    // ========== 全部46个消息发送完成 ==========
    // 延迟分组统计：
    // 0ms延迟: 8个PackInfo消息 (最高优先级)
    // 1ms延迟: 5个Status消息 (高优先级)
    // 2ms延迟: 18个Cell消息 (中高优先级，峰值负载)
    // 3ms延迟: 12个Temp消息 (中等优先级)
    // 4ms延迟: 2个OtherVolt消息 (低优先级)
    // 5ms延迟: 1个Version消息 (最低优先级)
}

/* BMS端：使用定时器自动广播消息 - 修正版 */
void bms_timer_broadcast_example(void)
{
    // 初始化定时器系统
    can_iahp_bms_timer_init();

    // 启动1ms定时器中断 (用户需要实现)
    // HAL_TIM_Base_Start_IT(&htim_1ms);

    // 测试：模拟50ms周期内的延迟发送
    // === 模拟50ms周期内的延迟发送 ===
    for (uint8_t ms = 0; ms < 6; ms++) {
        // 时刻 %dms:
        can_iahp_bms_process_delayed_messages(ms);
        // 发送完成
    }

    // 或者直接调用广播函数（兼容性测试）
    // can_iahp_bms_broadcast_all_messages();
}

/* 用户需要实现的CAN发送函数 */
void can_broadcast(uint32_t can_id, uint8_t *data, uint8_t length)
{
    // 用户实现CAN硬件发送
    // 示例：STM32 HAL库
    /*
    CAN_TxHeaderTypeDef TxHeader;
    uint32_t TxMailbox;

    TxHeader.IDE = CAN_ID_EXT;          // 扩展帧
    TxHeader.ExtId = can_id;            // 29位扩展ID
    TxHeader.RTR = CAN_RTR_DATA;        // 数据帧
    TxHeader.DLC = length;              // 数据长度
    TxHeader.TransmitGlobalTime = DISABLE;

    if (HAL_CAN_AddTxMessage(&hcan, &TxHeader, data, &TxMailbox) != HAL_OK) {
        // 发送失败处理
        Error_Handler();
    }
    */

    // 调试输出 (实际应用中删除)
    // CAN发送: ID=0x%08X, 长度=%d, 数据=[
    // for (int i = 0; i < length; i++) {
    //     printf("%02X ", data[i]);
    // }
    // printf("]\n");
}

/* 1ms定时器中断服务程序示例 - 修正版 */
void TIM_1ms_IRQHandler(void)
{
    // 调用修正后的BMS定时器处理函数
    // 这个函数现在会在每个1ms检查并发送对应延迟时间的消息
    can_iahp_bms_timer_1ms_handler();

    // 清除定时器中断标志 (用户需要根据具体硬件实现)
    // __HAL_TIM_CLEAR_IT(&htim_1ms, TIM_IT_UPDATE);
}

/*
 * 注意：此代码专用于BMS端广播消息 - 修正版
 * - 修正了定时器实现，真正实现1ms精度延迟分散
 * - 基于GenMsgDelayTime的物理量分组延迟策略
 * - 自动处理50ms周期和0~5ms延迟分散发送
 * - 瞬时峰值负载降低60.9%，关键消息延迟改善82.6%
 * - 其他ECU的接收功能需要根据具体需求单独实现
 */

/* 主函数示例 - 修正版 */
int main(void)
{
    // === CAN_IAHP_BMS 延迟分散发送示例 ===
    // BMS消息总数: 46个
    // 延迟策略: 物理量分组 (0-5ms)
    // 周期时间: 50ms

    // 系统初始化
    // SystemInit();
    // HAL_Init();

    // CAN硬件初始化 (用户需要实现)
    // MX_CAN_Init();
    // HAL_CAN_Start(&hcan);

    // === 手动广播示例 ===
    bms_manual_broadcast_example();

    // === 定时器自动广播示例 ===
    bms_timer_broadcast_example();

    // 实际应用中的主循环
    // === 进入主循环 ===
    // 在实际应用中，1ms定时器中断会自动处理消息发送

    /*
    // 实际应用代码示例：
    while(1) {
        // 更新BMS数据
        bms_update_data();

        // 其他BMS业务逻辑
        bms_protection_check();
        bms_balance_control();
        bms_soc_calculation();

        // 主循环延时
        HAL_Delay(10);
    }
    */

    return 0;
}
